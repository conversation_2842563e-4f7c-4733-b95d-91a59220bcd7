"""
SSE传输层实现模块

该模块实现了MCP协议的Server-Sent Events (SSE) 传输层，
用于在FastAPI应用中提供实时双向通信能力。

主要功能：
1. SSE连接管理
2. 消息路由和处理
3. 会话状态管理
4. 错误处理和重连机制
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from fastapi import Request, Response
from starlette.applications import Starlette
from starlette.routing import Route, Mount
from starlette.responses import StreamingResponse
from mcp.server.fastmcp import FastMCP
from mcp.server.sse import SseServerTransport

# 配置日志记录器
logger = logging.getLogger(__name__)


class SSETransportManager:
    """
    SSE传输管理器
    
    负责管理SSE连接、消息传输和会话状态。
    提供了完整的MCP协议支持和错误处理机制。
    """
    
    def __init__(self, mcp_server: FastMCP):
        """
        初始化SSE传输管理器
        
        Args:
            mcp_server: FastMCP服务器实例
        """
        self.mcp_server = mcp_server
        self.transport = SseServerTransport("/messages/")
        self.active_sessions: Dict[str, Any] = {}
        
        logger.info("SSE传输管理器初始化完成")
    
    async def handle_sse_connection(self, request: Request) -> StreamingResponse:
        """
        处理SSE连接请求
        
        建立SSE连接并管理MCP协议通信。
        每个连接都有唯一的会话ID用于状态管理。
        
        Args:
            request: FastAPI请求对象
            
        Returns:
            StreamingResponse: SSE流响应
        """
        session_id = str(uuid.uuid4())
        logger.info(f"新的SSE连接请求，会话ID: {session_id}")
        
        try:
            # 建立SSE连接并处理MCP协议
            async with self.transport.connect_sse(
                request.scope, 
                request.receive, 
                request._send
            ) as streams:
                # 记录活跃会话
                self.active_sessions[session_id] = {
                    "streams": streams,
                    "created_at": asyncio.get_event_loop().time()
                }
                
                logger.info(f"会话 {session_id} 连接建立成功")
                
                try:
                    # 运行MCP服务器处理逻辑
                    await self.mcp_server._mcp_server.run(
                        streams[0], 
                        streams[1], 
                        self.mcp_server._mcp_server.create_initialization_options()
                    )
                except Exception as e:
                    logger.error(f"会话 {session_id} 运行时错误: {str(e)}")
                    raise
                finally:
                    # 清理会话
                    if session_id in self.active_sessions:
                        del self.active_sessions[session_id]
                        logger.info(f"会话 {session_id} 已清理")
                        
        except Exception as e:
            logger.error(f"SSE连接处理失败，会话ID: {session_id}, 错误: {str(e)}")
            raise
    
    async def handle_post_message(self, request: Request) -> Response:
        """
        处理POST消息请求
        
        接收客户端发送的消息并转发给相应的会话处理。
        
        Args:
            request: FastAPI请求对象
            
        Returns:
            Response: HTTP响应
        """
        try:
            # 委托给传输层处理
            return await self.transport.handle_post_message(request)
        except Exception as e:
            logger.error(f"POST消息处理失败: {str(e)}")
            return Response(
                content=json.dumps({"error": "消息处理失败"}),
                status_code=500,
                media_type="application/json"
            )
    
    def get_session_count(self) -> int:
        """
        获取当前活跃会话数量
        
        Returns:
            int: 活跃会话数量
        """
        return len(self.active_sessions)
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        获取会话信息统计
        
        Returns:
            Dict[str, Any]: 包含会话统计信息的字典
        """
        current_time = asyncio.get_event_loop().time()
        session_info = {
            "total_sessions": len(self.active_sessions),
            "sessions": []
        }
        
        for session_id, session_data in self.active_sessions.items():
            uptime = current_time - session_data["created_at"]
            session_info["sessions"].append({
                "session_id": session_id,
                "uptime_seconds": round(uptime, 2)
            })
        
        return session_info


def create_sse_server(mcp_server: FastMCP) -> Starlette:
    """
    创建SSE服务器应用
    
    构建一个Starlette应用来处理SSE连接和消息路由。
    该应用可以挂载到FastAPI主应用中。
    
    Args:
        mcp_server: FastMCP服务器实例
        
    Returns:
        Starlette: 配置好的Starlette应用
    """
    logger.info("开始创建SSE服务器应用")
    
    # 创建传输管理器
    transport_manager = SSETransportManager(mcp_server)
    
    # 定义路由处理函数
    async def handle_sse(request: Request) -> StreamingResponse:
        """SSE连接处理端点"""
        return await transport_manager.handle_sse_connection(request)
    
    async def handle_messages(request: Request) -> Response:
        """消息处理端点"""
        return await transport_manager.handle_post_message(request)
    
    async def handle_status(request: Request) -> Response:
        """状态查询端点"""
        session_info = transport_manager.get_session_info()
        return Response(
            content=json.dumps(session_info, ensure_ascii=False),
            media_type="application/json"
        )
    
    # 创建路由配置
    routes = [
        Route("/sse/", endpoint=handle_sse, methods=["GET"]),
        Route("/status/", endpoint=handle_status, methods=["GET"]),
        # 直接使用传输管理器的消息处理方法
        Route("/messages/", endpoint=handle_messages, methods=["POST"]),
    ]
    
    # 创建Starlette应用
    sse_app = Starlette(routes=routes)
    
    logger.info("SSE服务器应用创建完成")
    return sse_app
