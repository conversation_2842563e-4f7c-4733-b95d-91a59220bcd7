"""
FastAPI主应用

这是MCP OMS服务器的主要FastAPI应用。
集成了MCP服务器和SSE传输层，提供完整的Web API和MCP协议支持。

主要功能：
1. FastAPI应用配置和启动
2. MCP服务器集成
3. SSE传输层挂载
4. 静态文件服务
5. API路由定义
6. 中间件配置
"""

import logging
import os
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from .mcp_server import create_mcp_server_instance, MCPServerConfig
from .transport.sse_transport import create_sse_server

# 配置日志记录器
logger = logging.getLogger(__name__)


# 全局变量存储MCP服务器实例
mcp_server_instance = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    FastAPI应用生命周期管理

    在应用启动时初始化MCP服务器，在关闭时清理资源。

    Args:
        app: FastAPI应用实例
    """
    global mcp_server_instance

    # 启动时初始化
    logger.info("FastAPI应用启动中...")

    try:
        # 创建MCP服务器配置
        config = MCPServerConfig(
            name="MCP OMS Server",
            version="1.0.0",
            description="基于FastAPI的MCP订单管理系统服务器",
            max_connections=100,
            log_level="INFO"
        )

        # 创建MCP服务器实例
        mcp_server_instance = create_mcp_server_instance(config)
        await mcp_server_instance.start()

        logger.info("MCP服务器初始化完成")

        # 创建SSE服务器并挂载
        sse_server = create_sse_server(mcp_server_instance.get_server())
        app.mount("/mcp", sse_server)

        logger.info("MCP SSE服务器已挂载到 /mcp")

        yield

    finally:
        # 关闭时清理
        logger.info("FastAPI应用关闭中...")

        if mcp_server_instance:
            await mcp_server_instance.stop()

        logger.info("FastAPI应用已关闭")


def create_app() -> FastAPI:
    """
    创建FastAPI应用实例
    
    配置所有必要的中间件、路由和挂载点。
    
    Returns:
        FastAPI: 配置好的FastAPI应用实例
    """
    # 创建FastAPI应用
    app = FastAPI(
        title="MCP OMS Server",
        description="基于FastAPI和MCP协议的订单管理系统服务器",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )
    
    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 在生产环境中应该限制具体的域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 挂载静态文件服务
    static_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")
    static_path = os.path.abspath(static_path)
    if os.path.exists(static_path):
        app.mount("/static", StaticFiles(directory=static_path), name="static")
        logger.info(f"静态文件服务已挂载: {static_path}")
    else:
        logger.warning(f"静态文件目录不存在: {static_path}")
    
    return app


# 创建应用实例
app = create_app()


@app.get("/", response_class=HTMLResponse)
async def root():
    """
    根路径处理器
    
    返回主页HTML，如果静态文件存在则重定向到测试页面。
    
    Returns:
        HTMLResponse: HTML响应
    """
    static_path = os.path.join(os.path.dirname(__file__), "..", "static", "index.html")
    
    if os.path.exists(static_path):
        # 如果存在静态测试页面，则重定向
        return HTMLResponse("""
        <!DOCTYPE html>
        <html>
        <head>
            <title>MCP OMS Server</title>
            <meta charset="utf-8">
        </head>
        <body>
            <script>
                window.location.href = '/static/index.html';
            </script>
            <p>正在跳转到测试页面... <a href="/static/index.html">点击这里手动跳转</a></p>
        </body>
        </html>
        """)
    else:
        # 返回简单的欢迎页面
        return HTMLResponse("""
        <!DOCTYPE html>
        <html>
        <head>
            <title>MCP OMS Server</title>
            <meta charset="utf-8">
        </head>
        <body>
            <h1>MCP OMS Server</h1>
            <p>基于FastAPI和MCP协议的订单管理系统服务器</p>
            <ul>
                <li><a href="/docs">API文档 (Swagger)</a></li>
                <li><a href="/redoc">API文档 (ReDoc)</a></li>
                <li><a href="/api/server-info">服务器信息</a></li>
                <li><a href="/api/health">健康检查</a></li>
            </ul>
        </body>
        </html>
        """)


@app.get("/api/health")
async def health_check():
    """
    健康检查端点
    
    返回服务器的健康状态信息。
    
    Returns:
        Dict[str, Any]: 健康状态信息
    """
    global mcp_server_instance
    
    return {
        "status": "healthy",
        "service": "MCP OMS Server",
        "version": "1.0.0",
        "mcp_server_ready": mcp_server_instance is not None,
        "timestamp": "2025-07-02T18:34:00Z"
    }


@app.get("/api/server-info")
async def get_server_info():
    """
    获取服务器信息端点
    
    返回详细的服务器配置和状态信息。
    
    Returns:
        Dict[str, Any]: 服务器信息
    """
    global mcp_server_instance
    
    if not mcp_server_instance:
        raise HTTPException(status_code=503, detail="MCP服务器未初始化")
    
    server_info = mcp_server_instance.get_server_info()
    
    return {
        "fastapi": {
            "title": app.title,
            "description": app.description,
            "version": app.version,
            "docs_url": app.docs_url,
            "redoc_url": app.redoc_url
        },
        "mcp_server": server_info,
        "endpoints": {
            "health": "/api/health",
            "server_info": "/api/server-info",
            "mcp_sse": "/mcp/sse/",
            "mcp_status": "/mcp/status/",
            "static_files": "/static/",
            "api_docs": "/docs"
        }
    }


@app.get("/api/tools")
async def list_tools():
    """
    列出所有可用的MCP工具
    
    Returns:
        Dict[str, Any]: 工具列表和描述
    """
    global mcp_server_instance
    
    if not mcp_server_instance:
        raise HTTPException(status_code=503, detail="MCP服务器未初始化")
    
    from .tools.sample_tools import TOOL_DEFINITIONS
    
    return {
        "total_tools": len(TOOL_DEFINITIONS),
        "tools": TOOL_DEFINITIONS
    }





def run_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """
    运行FastAPI服务器
    
    这是一个便捷函数，用于启动服务器。
    
    Args:
        host: 服务器主机地址
        port: 服务器端口
        reload: 是否启用自动重载（开发模式）
    """
    logger.info(f"启动MCP OMS服务器: http://{host}:{port}")
    
    uvicorn.run(
        "src.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )


if __name__ == "__main__":
    # 直接运行时启动服务器
    run_server(reload=True)
