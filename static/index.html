<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP OMS Server - 测试页面</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>MCP OMS Server 测试页面</h1>
            <p>基于FastAPI和MCP协议的订单管理系统服务器</p>
        </header>

        <div class="status-panel">
            <h2>连接状态</h2>
            <div class="status-indicator">
                <span id="connection-status" class="status-disconnected">未连接</span>
                <button id="connect-btn" onclick="connectToMCP()">连接MCP服务器</button>
                <button id="disconnect-btn" onclick="disconnectFromMCP()" disabled>断开连接</button>
            </div>
        </div>

        <div class="tools-panel">
            <h2>可用工具</h2>
            <div class="tool-grid">
                <!-- 计算器工具 -->
                <div class="tool-card">
                    <h3>计算器</h3>
                    <p>安全的数学表达式计算器</p>
                    <div class="tool-form">
                        <input type="text" id="calc-expression" placeholder="输入数学表达式，如：2+2*3" value="2+2*3">
                        <button onclick="callCalculator()">计算</button>
                    </div>
                </div>

                <!-- 文件列表工具 -->
                <div class="tool-card">
                    <h3>文件列表</h3>
                    <p>列出指定目录中的文件</p>
                    <div class="tool-form">
                        <input type="text" id="file-directory" placeholder="目录路径" value=".">
                        <label>
                            <input type="checkbox" id="file-show-hidden"> 显示隐藏文件
                        </label>
                        <button onclick="callListFiles()">列出文件</button>
                    </div>
                </div>

                <!-- 订单查询工具 -->
                <div class="tool-card">
                    <h3>订单查询</h3>
                    <p>查询订单信息</p>
                    <div class="tool-form">
                        <input type="text" id="order-id" placeholder="订单ID（可选）">
                        <select id="order-status">
                            <option value="">所有状态</option>
                            <option value="pending">待处理</option>
                            <option value="completed">已完成</option>
                            <option value="cancelled">已取消</option>
                        </select>
                        <input type="number" id="order-limit" placeholder="数量限制" value="10" min="1" max="100">
                        <button onclick="callQueryOrders()">查询订单</button>
                    </div>
                </div>

                <!-- 系统信息工具 -->
                <div class="tool-card">
                    <h3>系统信息</h3>
                    <p>获取服务器系统信息</p>
                    <div class="tool-form">
                        <button onclick="callSystemInfo()">获取系统信息</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="results-panel">
            <h2>执行结果</h2>
            <div class="results-container">
                <pre id="results-output">等待工具调用...</pre>
            </div>
            <div class="results-actions">
                <button onclick="clearResults()">清空结果</button>
                <button onclick="copyResults()">复制结果</button>
            </div>
        </div>

        <div class="logs-panel">
            <h2>连接日志</h2>
            <div class="logs-container">
                <pre id="logs-output">等待连接...</pre>
            </div>
            <div class="logs-actions">
                <button onclick="clearLogs()">清空日志</button>
            </div>
        </div>

        <div class="info-panel">
            <h2>服务器信息</h2>
            <div class="info-grid">
                <div class="info-item">
                    <strong>服务器地址:</strong>
                    <span id="server-url">加载中...</span>
                </div>
                <div class="info-item">
                    <strong>MCP端点:</strong>
                    <span id="mcp-endpoint">加载中...</span>
                </div>
                <div class="info-item">
                    <strong>API文档:</strong>
                    <a href="/docs" target="_blank">Swagger UI</a> |
                    <a href="/redoc" target="_blank">ReDoc</a>
                </div>
            </div>
            <button onclick="loadServerInfo()">刷新服务器信息</button>
        </div>
    </div>

    <script src="/static/js/mcp_client.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadServerInfo();
            updateServerUrl();
        });

        function updateServerUrl() {
            const serverUrl = window.location.origin;
            const mcpEndpoint = serverUrl + '/mcp/sse/';
            
            document.getElementById('server-url').textContent = serverUrl;
            document.getElementById('mcp-endpoint').textContent = mcpEndpoint;
        }

        async function loadServerInfo() {
            try {
                const response = await fetch('/api/server-info');
                const data = await response.json();
                
                 // 通过 mcpClient 实例调用 addLog 函数
                mcpClient.addLog('服务器信息加载成功');
                mcpClient.addLog(`MCP工具数量: ${data.mcp_server.registered_tools}`);
                mcpClient.addLog(`可用工具: ${data.mcp_server.tool_names.join(', ')}`);
                
            } catch (error) {
                // 通过 mcpClient 实例调用 addLog 函数
                mcpClient.addLog(`加载服务器信息失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
