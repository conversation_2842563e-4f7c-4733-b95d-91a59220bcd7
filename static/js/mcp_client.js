/**
 * MCP客户端JavaScript实现
 * 
 * 这个文件实现了与MCP服务器的SSE连接和工具调用功能。
 * 提供了完整的客户端API和用户界面交互。
 */

class MCPClient {
    constructor() {
        this.eventSource = null;
        this.isConnected = false;
        this.messageId = 1;
        this.pendingRequests = new Map();
        this.baseUrl = window.location.origin;
        this.sseUrl = `${this.baseUrl}/mcp/sse/`;
        this.messagesUrl = `${this.baseUrl}/mcp/messages/`;
    }

    /**
     * 连接到MCP服务器
     */
    async connect() {
        if (this.isConnected) {
            this.addLog('已经连接到MCP服务器', 'info');
            return;
        }

        try {
            this.updateConnectionStatus('connecting');
            this.addLog('正在连接到MCP服务器...', 'info');

            // 创建SSE连接
            this.eventSource = new EventSource(this.sseUrl);

            // 设置事件监听器
            this.eventSource.onopen = () => {
                this.isConnected = true;
                this.updateConnectionStatus('connected');
                this.addLog('成功连接到MCP服务器', 'success');
            };

            this.eventSource.onmessage = (event) => {
                this.handleMessage(event.data);
            };

            this.eventSource.onerror = (error) => {
                this.addLog(`SSE连接错误: ${error}`, 'error');
                this.handleDisconnection();
            };

        } catch (error) {
            this.addLog(`连接失败: ${error.message}`, 'error');
            this.handleDisconnection();
        }
    }

    /**
     * 断开与MCP服务器的连接
     */
    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        
        this.handleDisconnection();
        this.addLog('已断开与MCP服务器的连接', 'info');
    }

    /**
     * 处理断开连接
     */
    handleDisconnection() {
        this.isConnected = false;
        this.updateConnectionStatus('disconnected');
        
        // 清理待处理的请求
        for (const [id, request] of this.pendingRequests) {
            request.reject(new Error('连接已断开'));
        }
        this.pendingRequests.clear();
    }

    /**
     * 处理接收到的消息
     */
    handleMessage(data) {
        try {
            const message = JSON.parse(data);
            this.addLog(`收到消息: ${JSON.stringify(message, null, 2)}`, 'info');

            // 处理响应消息
            if (message.id && this.pendingRequests.has(message.id)) {
                const request = this.pendingRequests.get(message.id);
                this.pendingRequests.delete(message.id);

                if (message.error) {
                    request.reject(new Error(message.error.message || '未知错误'));
                } else {
                    request.resolve(message.result);
                }
            }

        } catch (error) {
            this.addLog(`解析消息失败: ${error.message}`, 'error');
        }
    }

    /**
     * 发送消息到MCP服务器
     */
    async sendMessage(method, params = {}) {
        if (!this.isConnected) {
            throw new Error('未连接到MCP服务器');
        }

        const messageId = this.messageId++;
        const message = {
            jsonrpc: '2.0',
            id: messageId,
            method: method,
            params: params
        };

        return new Promise((resolve, reject) => {
            // 存储待处理的请求
            this.pendingRequests.set(messageId, { resolve, reject });

            // 发送消息
            fetch(this.messagesUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(message)
            }).catch(error => {
                this.pendingRequests.delete(messageId);
                reject(error);
            });

            this.addLog(`发送消息: ${JSON.stringify(message, null, 2)}`, 'info');
        });
    }

    /**
     * 调用MCP工具
     */
    async callTool(toolName, args = {}) {
        try {
            this.addLog(`调用工具: ${toolName}`, 'info');
            
            const result = await this.sendMessage('tools/call', {
                name: toolName,
                arguments: args
            });

            this.addLog(`工具调用成功: ${toolName}`, 'success');
            return result;

        } catch (error) {
            this.addLog(`工具调用失败: ${toolName} - ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connection-status');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');

        if (statusElement) {
            statusElement.className = `status-${status}`;
            
            switch (status) {
                case 'connected':
                    statusElement.textContent = '已连接';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    break;
                case 'connecting':
                    statusElement.textContent = '连接中...';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = true;
                    break;
                case 'disconnected':
                default:
                    statusElement.textContent = '未连接';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    break;
            }
        }
    }

    /**
     * 添加日志条目
     */
    addLog(message, type = 'info') {
        const logsOutput = document.getElementById('logs-output');
        if (logsOutput) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logsOutput.textContent += logEntry;
            logsOutput.scrollTop = logsOutput.scrollHeight;
        }
        
        console.log(`[MCP Client] ${message}`);
    }

    /**
     * 显示结果
     */
    showResult(result) {
        const resultsOutput = document.getElementById('results-output');
        if (resultsOutput) {
            resultsOutput.textContent = JSON.stringify(result, null, 2);
        }
    }
}

// 创建全局MCP客户端实例
const mcpClient = new MCPClient();

/**
 * 连接到MCP服务器
 */
async function connectToMCP() {
    await mcpClient.connect();
}

/**
 * 断开MCP连接
 */
function disconnectFromMCP() {
    mcpClient.disconnect();
}

/**
 * 调用计算器工具
 */
async function callCalculator() {
    const expression = document.getElementById('calc-expression').value;
    
    if (!expression.trim()) {
        alert('请输入数学表达式');
        return;
    }

    try {
        const result = await mcpClient.callTool('calculator', {
            expression: expression
        });
        mcpClient.showResult(result);
    } catch (error) {
        mcpClient.showResult({ error: error.message });
    }
}

/**
 * 调用文件列表工具
 */
async function callListFiles() {
    const directory = document.getElementById('file-directory').value;
    const showHidden = document.getElementById('file-show-hidden').checked;

    try {
        const result = await mcpClient.callTool('list_files', {
            directory: directory || '.',
            show_hidden: showHidden
        });
        mcpClient.showResult(result);
    } catch (error) {
        mcpClient.showResult({ error: error.message });
    }
}

/**
 * 调用订单查询工具
 */
async function callQueryOrders() {
    const orderId = document.getElementById('order-id').value;
    const status = document.getElementById('order-status').value;
    const limit = parseInt(document.getElementById('order-limit').value) || 10;

    const params = { limit };
    if (orderId.trim()) params.order_id = orderId.trim();
    if (status) params.status = status;

    try {
        const result = await mcpClient.callTool('query_orders', params);
        mcpClient.showResult(result);
    } catch (error) {
        mcpClient.showResult({ error: error.message });
    }
}

/**
 * 调用系统信息工具
 */
async function callSystemInfo() {
    try {
        const result = await mcpClient.callTool('system_info');
        mcpClient.showResult(result);
    } catch (error) {
        mcpClient.showResult({ error: error.message });
    }
}

/**
 * 清空结果
 */
function clearResults() {
    const resultsOutput = document.getElementById('results-output');
    if (resultsOutput) {
        resultsOutput.textContent = '等待工具调用...';
    }
}

/**
 * 复制结果
 */
function copyResults() {
    const resultsOutput = document.getElementById('results-output');
    if (resultsOutput && resultsOutput.textContent.trim()) {
        navigator.clipboard.writeText(resultsOutput.textContent).then(() => {
            alert('结果已复制到剪贴板');
        }).catch(err => {
            console.error('复制失败:', err);
            alert('复制失败，请手动选择并复制');
        });
    }
}

/**
 * 清空日志
 */
function clearLogs() {
    const logsOutput = document.getElementById('logs-output');
    if (logsOutput) {
        logsOutput.textContent = '';
    }
}
